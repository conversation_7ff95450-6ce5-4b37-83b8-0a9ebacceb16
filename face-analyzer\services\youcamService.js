const https = require('https');
const crypto = require('crypto');

let cachedAccessToken = null;
let tokenExpiry = null;

// Function to generate id_token using RSA encryption
function generateIdToken(clientId, clientSecret) {
  const timestamp = new Date().getTime();
  const dataToEncrypt = `client_id=${clientId}&timestamp=${timestamp}`;

  // Format the public key properly
  const pemKey = `-----BEGIN PUBLIC KEY-----\n${clientSecret.match(/.{1,64}/g).join('\n')}\n-----END PUBLIC KEY-----`;

  try {
    // Use explicit RSA padding for compatibility
    const encryptOptions = {
      key: pemKey,
      padding: crypto.constants.RSA_PKCS1_PADDING
    };

    const encrypted = crypto.publicEncrypt(encryptOptions, Buffer.from(dataToEncrypt));
    const base64Result = encrypted.toString('base64');
    return base64Result;
  } catch (error) {
    console.error('Error generating id_token:', error);
    throw new Error('Failed to generate id_token');
  }
}

// Function to authenticate and get access token with timeout
async function getAccessToken() {
  // Check if we have a valid cached token (valid for 2 hours, refresh 10 minutes early)
  if (cachedAccessToken && tokenExpiry && Date.now() < tokenExpiry - 10 * 60 * 1000) {
    return cachedAccessToken;
  }

  const clientId = process.env.PERFECT_CORP_API_KEY;
  const clientSecret = process.env.YOUCAM_API_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error('Missing API credentials');
  }

  console.log('Authenticating with YouCam API...');

  const idToken = generateIdToken(clientId, clientSecret);

  const authPayload = {
    client_id: clientId,
    id_token: idToken
  };

  const options = {
    method: 'POST',
    hostname: 'yce-api-01.perfectcorp.com',
    port: null,
    path: '/s2s/v1.0/client/auth',
    headers: {
      'content-type': 'application/json'
    },
    timeout: 30000 // 30 second timeout for auth
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, function (res) {
      const chunks = [];

      res.on('data', function (chunk) {
        chunks.push(chunk);
      });

      res.on('end', function () {
        const body = Buffer.concat(chunks);
        const responseText = body.toString();

        console.log('Auth response status:', res.statusCode);

        try {
          const responseData = JSON.parse(responseText);

          if (res.statusCode >= 200 && res.statusCode < 300) {
            cachedAccessToken = responseData.result.access_token;
            tokenExpiry = Date.now() + 2 * 60 * 60 * 1000; // 2 hours from now
            resolve(cachedAccessToken);
          } else {
            reject(new Error(`Authentication failed: HTTP ${res.statusCode}: ${responseData.error || responseText}`));
          }
        } catch (parseError) {
          reject(new Error(`Authentication failed: HTTP ${res.statusCode}: ${responseText}`));
        }
      });
    });

    req.on('error', function (error) {
      console.error('Auth request error:', error);
      reject(new Error(`Authentication request failed: ${error.message}`));
    });

    req.on('timeout', function() {
      req.destroy();
      reject(new Error('Authentication request timed out'));
    });

    req.write(JSON.stringify(authPayload));
    req.end();
  });
}

exports.uploadImage = async (imageBase64) => {
  const accessToken = await getAccessToken();
  const imageBuffer = Buffer.from(imageBase64, 'base64');
  
  const options = {
    method: 'POST',
    hostname: 'yce-api-01.perfectcorp.com',
    port: null,
    path: '/s2s/v1.1/file/skin-analysis',
    headers: {
      Authorization: `Bearer ${accessToken}`,
      'content-type': 'application/json'
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, function (res) {
      const chunks = [];
      res.on('data', function (chunk) {
        chunks.push(chunk);
      });
      res.on('end', function () {
        const body = Buffer.concat(chunks);
        const responseText = body.toString();
        try {
          const responseData = JSON.parse(responseText);
          resolve(responseData);
        } catch (error) {
          reject(new Error('Failed to parse response'));
        }
      });
    });
    
    req.on('error', function (error) {
      reject(error);
    });
    
    req.write(JSON.stringify({
      files: [{content_type: 'image/jpg', file_name: 'my-selfie.jpg', file_size: imageBuffer.length}]
    }));
    req.end();
  });
};

exports.startAnalysis = async (fileId) => {
  const accessToken = await getAccessToken();
    console.log('accessToken', accessToken)

  const options = {
    method: 'POST',
    hostname: 'yce-api-01.perfectcorp.com',
    port: null,
    path: '/s2s/v1.0/task/skin-analysis',
    headers: {
      Authorization: `Bearer ${accessToken}`,
      'content-type': 'application/json'
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, function (res) {
      const chunks = [];
      res.on('data', function (chunk) {
        chunks.push(chunk);
      });
      res.on('end', function () {
        const body = Buffer.concat(chunks);
        const responseText = body.toString();
        try {
          const responseData = JSON.parse(responseText);
          resolve(responseData);
        } catch (error) {
          reject(new Error('Failed to parse response'));
        }
      });
    });
    
    req.on('error', function (error) {
      reject(error);
    });
    
    req.write(JSON.stringify({
      request_id: 0,
      payload: {
        file_sets: {src_ids: [fileId]},
        actions: [
          {
            id: 0,
            params: {},
            dst_actions: ['hd_wrinkle', 'hd_pore', 'hd_texture', 'hd_acne']
          }
        ]
      }
    }));
    req.end();
  });
};


exports.getAccessToken = getAccessToken;

exports.getAnalysisResult = async (taskId) => {
  const accessToken = await getAccessToken();
  
  const options = {
    method: 'GET',
    hostname: 'yce-api-01.perfectcorp.com',
    port: null,
    path: `/s2s/v1.0/task/skin-analysis?task_id=${encodeURIComponent(taskId)}`,
    headers: {
      Authorization: `Bearer ${accessToken}`
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, function (res) {
      const chunks = [];
      res.on('data', function (chunk) {
        chunks.push(chunk);
      });
      res.on('end', function () {
        const body = Buffer.concat(chunks);
        const responseText = body.toString();
        try {
          const responseData = JSON.parse(responseText);
          resolve(responseData);
        } catch (error) {
          reject(new Error('Failed to parse response'));
        }
      });
    });
    
    req.on('error', function (error) {
      reject(error);
    });
    
    req.end();
  });
};