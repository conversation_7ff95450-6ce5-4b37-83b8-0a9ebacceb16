const https = require('https');
const crypto = require('crypto');

let cachedAccessToken = null;
let tokenExpiry = null;

// Function to generate id_token using RSA encryption
function generateIdToken(clientId, clientSecret) {
  const timestamp = new Date().getTime();
  const dataToEncrypt = `client_id=${clientId}&timestamp=${timestamp}`;

  // Format the public key properly
  const pemKey = `-----BEGIN PUBLIC KEY-----\n${clientSecret.match(/.{1,64}/g).join('\n')}\n-----END PUBLIC KEY-----`;

  try {
    // Use explicit RSA padding for compatibility
    const encryptOptions = {
      key: pemKey,
      padding: crypto.constants.RSA_PKCS1_PADDING
    };

    const encrypted = crypto.publicEncrypt(encryptOptions, Buffer.from(dataToEncrypt));
    const base64Result = encrypted.toString('base64');
    return base64Result;
  } catch (error) {
    console.error('Error generating id_token:', error);
    throw new Error('Failed to generate id_token');
  }
}

// Function to authenticate and get access token with timeout
async function getAccessToken() {
  // Check if we have a valid cached token (valid for 2 hours, refresh 10 minutes early)
  if (cachedAccessToken && tokenExpiry && Date.now() < tokenExpiry - 10 * 60 * 1000) {
    return cachedAccessToken;
  }

  const clientId = process.env.PERFECT_CORP_API_KEY;
  const clientSecret = process.env.YOUCAM_API_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error('Missing API credentials');
  }

  console.log('Authenticating with YouCam API...');

  const idToken = generateIdToken(clientId, clientSecret);

  const authPayload = {
    client_id: clientId,
    id_token: idToken
  };

  const options = {
    method: 'POST',
    hostname: 'yce-api-01.perfectcorp.com',
    port: null,
    path: '/s2s/v1.0/client/auth',
    headers: {
      'content-type': 'application/json'
    },
    timeout: 30000 // 30 second timeout for auth
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, function (res) {
      const chunks = [];

      res.on('data', function (chunk) {
        chunks.push(chunk);
      });

      res.on('end', function () {
        const body = Buffer.concat(chunks);
        const responseText = body.toString();

        console.log('Auth response status:', res.statusCode);

        try {
          const responseData = JSON.parse(responseText);

          if (res.statusCode >= 200 && res.statusCode < 300) {
            cachedAccessToken = responseData.result.access_token;
            tokenExpiry = Date.now() + 2 * 60 * 60 * 1000; // 2 hours from now
            resolve(cachedAccessToken);
          } else {
            reject(new Error(`Authentication failed: HTTP ${res.statusCode}: ${responseData.error || responseText}`));
          }
        } catch (parseError) {
          reject(new Error(`Authentication failed: HTTP ${res.statusCode}: ${responseText}`));
        }
      });
    });

    req.on('error', function (error) {
      console.error('Auth request error:', error);
      reject(new Error(`Authentication request failed: ${error.message}`));
    });

    req.on('timeout', function() {
      req.destroy();
      reject(new Error('Authentication request timed out'));
    });

    req.write(JSON.stringify(authPayload));
    req.end();
  });
}

exports.uploadImage = async (imageBase64) => {
  const accessToken = await getAccessToken();
  const imageBuffer = Buffer.from(imageBase64, 'base64');

  // Generate boundary for multipart form data
  const boundary = '----formdata-' + Math.random().toString(36).substring(2, 15);

  // Create multipart form data
  const formData = [];

  // Add form field for file
  formData.push(`--${boundary}\r\n`);
  formData.push(`Content-Disposition: form-data; name="file"; filename="my-selfie.jpg"\r\n`);
  formData.push(`Content-Type: image/jpeg\r\n\r\n`);

  // Convert string parts to buffer and combine with image buffer
  const formDataStart = Buffer.from(formData.join(''));
  const formDataEnd = Buffer.from(`\r\n--${boundary}--\r\n`);
  const totalBuffer = Buffer.concat([formDataStart, imageBuffer, formDataEnd]);

  const options = {
    method: 'POST',
    hostname: 'yce-api-01.perfectcorp.com',
    port: null,
    path: '/s2s/v1.1/file/skin-analysis',
    headers: {
      Authorization: `Bearer ${accessToken}`,
      'Content-Type': `multipart/form-data; boundary=${boundary}`,
      'Content-Length': totalBuffer.length
    },
    timeout: 60000 // 60 second timeout for file upload
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, function (res) {
      const chunks = [];
      res.on('data', function (chunk) {
        chunks.push(chunk);
      });
      res.on('end', function () {
        const body = Buffer.concat(chunks);
        const responseText = body.toString();

        console.log('Upload response status:', res.statusCode);
        console.log('Upload response:', responseText);

        try {
          const responseData = JSON.parse(responseText);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(responseData);
          } else {
            reject(new Error(`Upload failed: HTTP ${res.statusCode}: ${responseData.error || responseText}`));
          }
        } catch (error) {
          reject(new Error(`Upload failed: HTTP ${res.statusCode}: ${responseText}`));
        }
      });
    });

    req.on('error', function (error) {
      console.error('Upload request error:', error);
      reject(new Error(`Upload request failed: ${error.message}`));
    });

    req.on('timeout', function() {
      req.destroy();
      reject(new Error('Upload request timed out'));
    });

    // Write the multipart form data
    req.write(totalBuffer);
    req.end();
  });
};

exports.startAnalysis = async (fileId) => {
  const accessToken = await getAccessToken();
    console.log('accessToken', accessToken)

  const options = {
    method: 'POST',
    hostname: 'yce-api-01.perfectcorp.com',
    port: null,
    path: '/s2s/v1.0/task/skin-analysis',
    headers: {
      Authorization: `Bearer ${accessToken}`,
      'content-type': 'application/json'
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, function (res) {
      const chunks = [];
      res.on('data', function (chunk) {
        chunks.push(chunk);
      });
      res.on('end', function () {
        const body = Buffer.concat(chunks);
        const responseText = body.toString();
        try {
          const responseData = JSON.parse(responseText);
          resolve(responseData);
        } catch (error) {
          reject(new Error('Failed to parse response'));
        }
      });
    });
    
    req.on('error', function (error) {
      reject(error);
    });
    
    req.write(JSON.stringify({
      request_id: 0,
      payload: {
        file_sets: {src_ids: [fileId]},
        actions: [
          {
            id: 0,
            params: {},
            dst_actions: ['hd_wrinkle', 'hd_pore', 'hd_texture', 'hd_acne']
          }
        ]
      }
    }));
    req.end();
  });
};


exports.getAccessToken = getAccessToken;

exports.getAnalysisResult = async (taskId) => {
  const accessToken = await getAccessToken();
  
  const options = {
    method: 'GET',
    hostname: 'yce-api-01.perfectcorp.com',
    port: null,
    path: `/s2s/v1.0/task/skin-analysis?task_id=${encodeURIComponent(taskId)}`,
    headers: {
      Authorization: `Bearer ${accessToken}`
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, function (res) {
      const chunks = [];
      res.on('data', function (chunk) {
        chunks.push(chunk);
      });
      res.on('end', function () {
        const body = Buffer.concat(chunks);
        const responseText = body.toString();
        try {
          const responseData = JSON.parse(responseText);
          resolve(responseData);
        } catch (error) {
          reject(new Error('Failed to parse response'));
        }
      });
    });
    
    req.on('error', function (error) {
      reject(error);
    });
    
    req.end();
  });
};