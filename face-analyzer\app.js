require('dotenv').config();
const express = require('express');
const multer = require('multer');
const faceRoutes = require('./routes/faceRoutes');

const app = express();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Make upload middleware available to routes
app.locals.upload = upload;

app.use(express.json({ limit: '10mb' }));

app.use('/api/face', faceRoutes);

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
