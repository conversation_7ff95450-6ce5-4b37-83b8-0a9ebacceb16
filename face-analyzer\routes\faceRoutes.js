const express = require('express');
const multer = require('multer');
const router = express.Router();
const {
  uploadImage,
  startAnalysis,
  getAnalysisResult,
  analyzeFace,

} = require('../controllers/faceController');

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

router.post('/upload', upload.single('image'), uploadImage);
router.post('/start-analysis', startAnalysis); 
router.get('/result/:taskId', getAnalysisResult); 
// Main analysis endpoint
router.post('/analyze', analyzeFace);



module.exports = router;
