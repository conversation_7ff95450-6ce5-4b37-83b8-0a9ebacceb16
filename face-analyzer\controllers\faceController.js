const { uploadImage, startAnalysis, getAnalysisResult } = require('../services/youcamService');

exports.uploadImage = async (req, res) => {
  try {
    const img = req.body.image;
    if (!img) {
      return res.status(400).json({
        error: 'No image provided'
      });
    }

    const fileId = await uploadImage(img);
    res.json({
      success: true,
      fileId
    });

  } catch (error) {
    res.status(500).json({
      error: error.message
    });
  }
};

exports.startAnalysis = async (req, res) => {
  try {
    const { fileId } = req.body;
    if (!fileId) {
      return res.status(400).json({ error: 'fileId is required' });
    }

    const taskId = await startAnalysis(fileId);
    res.json({
      success: true,
      taskId
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

exports.getAnalysisResult = async (req, res) => {
  try {
    const { taskId } = req.params;
    if (!taskId) {
      return res.status(400).json({ error: 'taskId is required' });
    }

    const result = await getAnalysisResult(taskId);
    res.json({
      success: true,
      result
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

exports.analyzeFace = async (req, res) => {
  try {
    let img = req.body.image;
    if (!img) {
      return res.status(400).json({ error: 'No image provided' });
    }

    if (img.includes(',')) {
      img = img.split(',')[1];
    }

    const fileId = await uploadImage(img);
    const taskId = await startAnalysis(fileId);
    const result = await getAnalysisResult(taskId);

    res.json({
      success: true,
      result,
      fileId,
      taskId
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

